#!/bin/bash

# Script to generate base64 encoded Docker config for Kubernetes secret
# Usage: ./generate-docker-config.sh <username> <password> <email>

if [ $# -ne 3 ]; then
    echo "Usage: $0 <dockerhub-username> <dockerhub-password> <email>"
    echo "Example: $0 <NAME_EMAIL>"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
EMAIL=$3

# Create base64 encoded auth string (username:password)
AUTH=$(echo -n "$USERNAME:$PASSWORD" | base64 -w 0)

# Create the Docker config JSON
DOCKER_CONFIG=$(cat <<EOF
{
  "auths": {
    "https://index.docker.io/v1/": {
      "username": "$USERNAME",
      "password": "$PASSWORD",
      "email": "$EMAIL",
      "auth": "$AUTH"
    }
  }
}
EOF
)

# Base64 encode the entire config
ENCODED_CONFIG=$(echo -n "$DOCKER_CONFIG" | base64 -w 0)

echo "Generated Docker config (base64 encoded):"
echo "$ENCODED_CONFIG"
echo ""
echo "Use this value in your dockerhub-secret.yaml file under data:.dockerconfigjson"
