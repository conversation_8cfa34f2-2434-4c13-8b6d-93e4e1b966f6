#!/bin/bash

# Create DockerHub secret for private image authentication
# Replace the values below with your actual DockerHub credentials

DOCKERHUB_USERNAME="your-dockerhub-username"
DOCKERHUB_PASSWORD="your-dockerhub-password-or-token"
DOCKERHUB_EMAIL="<EMAIL>"
SECRET_NAME="dockerhub-secret"
NAMESPACE="blacking-ops-dev"

# Create the secret
kubectl create secret docker-registry $SECRET_NAME \
  --docker-server=https://index.docker.io/v1/ \
  --docker-username=$DOCKERHUB_USERNAME \
  --docker-password=$DOCKERHUB_PASSWORD \
  --docker-email=$DOCKERHUB_EMAIL \
  --namespace=$NAMESPACE

echo "DockerHub secret '$SECRET_NAME' created in namespace '$NAMESPACE'"
echo "Don't forget to add imagePullSecrets to your deployment!"
